
:root {
  --main-bg-color: #f6f6f2;
  --second-bg-color:  #E76A00;
  --main-color: #48392b;
}
*{
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

header {
  background-color: var(--second-bg-color);
  color: white;
  height: 4rem;
  display: flex;
  justify-content: center;
  align-items: center;
  position: sticky;
  top: 0;
}

body {
  background-color: var(--main-bg-color);
  color: var(--main-color);
  font-family: sans-serif;
}

main {
  width: 80%;
  margin: 1rem auto;
  
}

h2, h3 {
  color:var( --second-bg-color);
}

.posts {
  border: 3px var( --second-bg-color) solid;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem; 
}

h2 {
  align-self: center;
}

.post {
  background-color: white;
  border-radius: 10px;
  box-shadow:0 5px 10px 0 rgba(17, 12, 46, 0.8);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.post p {
  line-height: 1.5;
}





